package org.library.crud.model;

import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;

@Entity
public class Person {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private long id;

    private String name;
    private int age;

    // Constructors, Getters and Setters
    public Person() {}

    public Person(String name, int age) {
        this.name = name;
        this.age = age;
    }

    // Standard getters and setters
}
